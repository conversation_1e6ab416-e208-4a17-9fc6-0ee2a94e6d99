import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms';
import { zod4 } from 'sveltekit-superforms/adapters';
import { schema } from '$lib/schema';


export const load = async () => {
    const form = await superValidate(zod4(schema));
    return { form };
};

export const actions = {
    default: async ({ request }) => {
        const form = await superValidate(request, zod4(schema));

        if (!form.valid) {
            return fail(400, { form });
        }
        
        return message(form, 'Bạn đã đăng ký thành công. <PERSON><PERSON><PERSON> tra email để xác nhận.');
    }
};