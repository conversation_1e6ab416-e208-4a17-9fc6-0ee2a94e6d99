import { z } from 'zod/v4';


const zodSATScore = z.int().min(400, '<PERSON><PERSON><PERSON><PERSON> SAT phải ít nhất 400').max(1600, '<PERSON><PERSON><PERSON><PERSON> SAT phải tối đa 1600').multipleOf(10, '<PERSON><PERSON><PERSON><PERSON> SAT phải là bội số của 10');
const zodSATDates = ["23-08-2025", "13-09-2025", "04-10-2025", "08-11-2025", "06-12-2025", "14-03-2026", "02-05-2026", "06-06-2026", "Chưa xác định"] as const;

export const schema = z.object({
    name: z.string().min(1, 'Tên là bắt buộc'),
    email: z.email('Email không hợp lệ'),
    grade: z.int().min(1, '<PERSON>ớ<PERSON> là bắt buộc').max(13, '<PERSON>ớ<PERSON> phải tối đa là 13').default(NaN),
    test_date: z.enum(zodSATDates),
    current_sat: zodSATScore.default(400),
    target_sat: zodSATScore.default(1600),
    'cf-turnstile-response': z.string().nonempty('Please complete captcha')
});