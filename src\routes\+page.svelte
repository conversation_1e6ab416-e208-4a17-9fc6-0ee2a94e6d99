<script lang="ts">
	import { Button, H1, Input, P1 } from '$lib/ui';
	import H2 from '$lib/ui/typography/H2.svelte';
	import { superForm } from 'sveltekit-superforms';
	import { zod4Client } from 'sveltekit-superforms/adapters';
	import { Turnstile } from 'svelte-turnstile';
	import { schema } from '$lib/schema';
	import { PUBLIC_TURNSTILE_SITE_KEY } from '$env/static/public';

	let { data } = $props();

	// Call this to reset the turnstile
	let reset = $state<() => void>();

	// Client API:
	const { form, errors, message, constraints, enhance } = superForm(data.form, {
		validators: zod4Client(schema),
		onUpdate() {
			reset?.();
		}
	});
</script>

<svelte:head>
	<title>DSAT16 Webinar</title>
</svelte:head>

<section>
	{#if !$message}
		<div class="content-wrapper">
			<div class="content-left">
				<div class="logo">
					<img src="/dsat16_logo.png" alt="dsat16 logo" width="32" height="32" />
					<H1>DSAT16</H1>
				</div>
				<P1> XXX Xth | 8 AM PST</P1>
				<H2>Hướng dẫn tự học SAT</H2>
				<P1>Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum Lorem Ipsum</P1>
			</div>
			<form method="POST" class="content-right" use:enhance>
				<div class="inline">
					<Input fullWidth placeholder="Tên" label="Họ và tên" name="name" bind:value={$form.name} error={$errors.name?.[0]} constraints={$constraints.name} />
					<Input fullWidth placeholder="Email" label="Email" name="email" type="email" bind:value={$form.email} error={$errors.email?.[0]} constraints={$constraints.email} />
				</div>

				<div class="inline">
					<Input fullWidth placeholder="Điền 13 nếu đã tốt nghiệp" label="Lớp" name="grade" type="number" bind:value={$form.grade} error={$errors.grade?.[0]} constraints={$constraints.grade} />
					<Input fullWidth label="Ngày thi dự kiến" name="test_date" bind:value={$form.test_date} error={$errors.test_date?.[0]} constraints={$constraints.test_date} />
				</div>

				<div class="inline">
					<Input fullWidth label="Điểm SAT hiện tại" name="current_sat" type="number" bind:value={$form.current_sat} error={$errors.current_sat?.[0]} constraints={$constraints.current_sat} />
					<Input fullWidth label="Điểm SAT mong muốn" name="target_sat" type="number" bind:value={$form.target_sat} error={$errors.target_sat?.[0]} constraints={$constraints.target_sat} />
				</div>

				<Turnstile siteKey={PUBLIC_TURNSTILE_SITE_KEY} bind:reset />
				<Button type="submit">Đăng ký</Button>
			</form>
		</div>
	{:else}
		<div class="content-right">
			<H2>Đăng ký thành công</H2>
			<P1>{message}</P1>
		</div>
	{/if}
</section>

<style>
    section {
        padding: 4rem;
        background: var(--white);
        width: 100%;
        border-bottom: 0.25rem solid var(--pitch-black);

        display: flex;
        justify-content: center;
        align-items: center;
    }

    .content-wrapper {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: 1440px;
		gap: 2rem;
    }

	.content-right {
		display: flex;
		flex-direction: column;
		gap: 1rem;

		background: var(--light-aquamarine);

		border: 1px solid var(--pitch-black);
		border-radius: 1.25rem;
		padding: 4rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
	}

	.content-left, .content-right {
		flex: 1 1 0;
	}

	.inline {
		display: flex;
		gap: 2rem;
	}

    @media (max-width: 768px) {
        section {
            padding: 4rem 1rem;
        }
    }
</style>